@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import "tailwindcss";

:root {
  /* Colors - Professional Remote Work Theme */
  --background: #ffffff;
  --foreground: #1f2937;
  --muted: #f9fafb;
  --muted-foreground: #6b7280;
  --accent: #3b82f6;
  --accent-foreground: #ffffff;
  --secondary: #f3f4f6;
  --secondary-foreground: #374151;
  --border: #e5e7eb;
  --input: #f9fafb;
  --ring: #3b82f6;

  /* Semantic colors */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;

  /* Typography */
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
}


* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography Styles */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: 0.5em;
  color: var(--foreground);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
  margin-bottom: 1rem;
  color: var(--muted-foreground);
}

a {
  color: var(--accent);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--accent);
  opacity: 0.8;
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;
  text-decoration: none;
}

.btn-primary {
  background: var(--accent);
  color: var(--accent-foreground);
}

.btn-primary:hover {
  background: var(--accent);
  opacity: 0.9;
  transform: translateY(-1px);
}

.btn-secondary {
  background: var(--secondary);
  color: var(--secondary-foreground);
}

.btn-secondary:hover {
  background: var(--secondary);
  opacity: 0.8;
}

.btn-outline {
  background: transparent;
  color: var(--accent);
  border: 1px solid var(--border);
}

.btn-outline:hover {
  background: var(--accent);
  color: var(--accent-foreground);
}

/* Card Styles */
.card {
  background: var(--background);
  border: 1px solid var(--border);
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* Form Styles */
.form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border);
  border-radius: 0.5rem;
  background: var(--input);
  color: var(--foreground);
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--ring);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

/* Utility Classes */
.text-muted {
  color: var(--muted-foreground);
}

.text-accent {
  color: var(--accent);
}

.bg-muted {
  background: var(--muted);
}

.border-default {
  border-color: var(--border);
}

/* Content Styles */
.prose {
  max-width: none;
}

.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
  color: var(--foreground);
  font-weight: 600;
}

.prose p {
  color: var(--muted-foreground);
  margin-bottom: 1.25rem;
}

.prose ul, .prose ol {
  margin-bottom: 1.25rem;
  padding-left: 1.5rem;
}

.prose li {
  margin-bottom: 0.5rem;
  color: var(--muted-foreground);
}

.prose blockquote {
  border-left: 4px solid var(--accent);
  padding-left: 1rem;
  margin: 1.5rem 0;
  font-style: italic;
  color: var(--muted-foreground);
}

.prose code {
  background: var(--muted);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  color: var(--accent);
}

.prose pre {
  background: var(--muted);
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1.5rem 0;
}

.prose pre code {
  background: none;
  padding: 0;
  color: var(--foreground);
}
