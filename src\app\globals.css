@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@import "tailwindcss";

/* RemoteWork Blog Design System - Premium & Professional */

@layer base {
  :root {
    /* Primary Brand Colors */
    --primary: 221 83% 53%;         /* Professional Blue #3b82f6 */
    --primary-foreground: 0 0% 100%;
    --primary-light: 221 91% 91%;   /* Light blue tint */
    --primary-dark: 221 83% 43%;    /* Darker blue */

    /* Background System */
    --background: 0 0% 100%;         /* Pure white */
    --background-alt: 220 14% 96%;   /* Subtle gray #f9fafb */
    --background-subtle: 220 13% 91%; /* Light gray sections */
    --foreground: 222 47% 11%;       /* Near black text */

    /* Content Colors */
    --muted: 220 14% 96%;           /* Light backgrounds */
    --muted-foreground: 220 9% 46%; /* Subtle text #6b7280 */
    --accent: 221 83% 53%;          /* Same as primary */
    --accent-foreground: 0 0% 100%;

    /* Semantic Colors */
    --success: 142 71% 45%;         /* Green #10b981 */
    --success-foreground: 0 0% 100%;
    --warning: 32 95% 44%;          /* Amber #f59e0b */
    --warning-foreground: 0 0% 100%;
    --destructive: 0 84% 60%;       /* Red #ef4444 */
    --destructive-foreground: 0 0% 100%;

    /* UI Elements */
    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;
    --border: 220 13% 91%;          /* Subtle borders */
    --input: 220 13% 91%;
    --ring: 221 83% 53%;            /* Focus rings */

    /* Content Hierarchy */
    --content-primary: 222 47% 11%;    /* Main headings */
    --content-secondary: 215 25% 27%;  /* Subheadings #374151 */
    --content-body: 220 9% 46%;        /* Body text #6b7280 */
    --content-caption: 220 9% 60%;     /* Meta text, captions */

    /* Gradients */
    --gradient-hero: linear-gradient(135deg, hsl(221 91% 91%) 0%, hsl(226 100% 94%) 100%);
    --gradient-card: linear-gradient(145deg, hsl(0 0% 100%) 0%, hsl(220 14% 98%) 100%);
    --gradient-cta: linear-gradient(135deg, hsl(221 83% 53%) 0%, hsl(221 83% 48%) 100%);

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 hsl(220 13% 91% / 0.05);
    --shadow-md: 0 4px 6px -1px hsl(220 13% 91% / 0.1), 0 2px 4px -1px hsl(220 13% 91% / 0.06);
    --shadow-lg: 0 10px 15px -3px hsl(220 13% 91% / 0.1), 0 4px 6px -2px hsl(220 13% 91% / 0.05);
    --shadow-xl: 0 20px 25px -5px hsl(220 13% 91% / 0.1), 0 10px 10px -5px hsl(220 13% 91% / 0.04);
    --shadow-card: 0 4px 20px -2px hsl(221 83% 53% / 0.08);
    --shadow-button: 0 4px 14px 0 hsl(221 83% 53% / 0.25);

    /* Spacing Scale */
    --space-xs: 0.25rem;    /* 4px */
    --space-sm: 0.5rem;     /* 8px */
    --space-md: 1rem;       /* 16px */
    --space-lg: 1.5rem;     /* 24px */
    --space-xl: 2rem;       /* 32px */
    --space-2xl: 3rem;      /* 48px */
    --space-3xl: 4rem;      /* 64px */

    /* Typography Scale */
    --text-xs: 0.75rem;     /* 12px */
    --text-sm: 0.875rem;    /* 14px */
    --text-base: 1rem;      /* 16px */
    --text-lg: 1.125rem;    /* 18px */
    --text-xl: 1.25rem;     /* 20px */
    --text-2xl: 1.5rem;     /* 24px */
    --text-3xl: 1.875rem;   /* 30px */
    --text-4xl: 2.25rem;    /* 36px */
    --text-5xl: 3rem;       /* 48px */
    --text-6xl: 3.75rem;    /* 60px */

    /* Animation Timing */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);

    --radius: 0.75rem;
    --radius-sm: 0.5rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }

  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
    font-variation-settings: "opsz" 32;
  }

  /* Typography System */
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight;
    color: hsl(var(--content-primary));
    line-height: 1.3;
  }

  h1 { font-size: var(--text-5xl); font-weight: 700; }
  h2 { font-size: var(--text-4xl); font-weight: 600; }
  h3 { font-size: var(--text-3xl); font-weight: 600; }
  h4 { font-size: var(--text-2xl); font-weight: 600; }
  h5 { font-size: var(--text-xl); font-weight: 600; }
  h6 { font-size: var(--text-lg); font-weight: 600; }

  p {
    color: hsl(var(--content-body));
    line-height: 1.7;
    font-size: var(--text-base);
  }

  .lead {
    color: hsl(var(--content-secondary));
    font-size: var(--text-lg);
    line-height: 1.6;
    font-weight: 400;
  }

  .caption {
    color: hsl(var(--content-caption));
    font-size: var(--text-sm);
    line-height: 1.5;
  }
}

@layer components {
  /* Custom Button Variants */
  .btn-primary {
    @apply inline-flex items-center justify-center rounded-lg px-6 py-3 font-medium text-sm;
    @apply transition-all duration-300 ease-in-out;
    background: var(--gradient-cta);
    color: hsl(var(--primary-foreground));
    box-shadow: var(--shadow-button);
  }

  .btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px 0 hsl(221 83% 53% / 0.35);
  }

  .btn-secondary {
    @apply inline-flex items-center justify-center rounded-lg px-6 py-3 font-medium text-sm;
    @apply border-2 transition-all duration-300 ease-in-out;
    border-color: hsl(var(--border));
    background: hsl(var(--background));
    color: hsl(var(--content-primary));
  }

  .btn-secondary:hover {
    border-color: hsl(var(--primary));
    color: hsl(var(--primary));
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }

  /* Card Components */
  .card-elevated {
    @apply rounded-xl border;
    background: var(--gradient-card);
    border-color: hsl(var(--border));
    box-shadow: var(--shadow-card);
  }

  .card-elevated:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
    transition: all var(--transition-normal);
  }

  /* Focus States */
  .focus-ring {
    @apply outline-none ring-2 ring-offset-2;
    ring-color: hsl(var(--ring));
  }

  /* Content Sections */
  .section-padding {
    @apply py-16 md:py-24;
  }

  .container-content {
    @apply mx-auto max-w-7xl px-4 sm:px-6 lg:px-8;
  }

  /* Gradients */
  .gradient-hero {
    background: var(--gradient-hero);
  }

  .gradient-cta {
    background: var(--gradient-cta);
  }

  /* Reading Experience */
  .prose-content {
    @apply max-w-3xl mx-auto;
  }

  .prose-content p {
    @apply mb-6;
    font-size: var(--text-lg);
    line-height: 1.7;
  }

  .prose-content h2 {
    @apply mt-12 mb-6;
  }

  .prose-content h3 {
    @apply mt-10 mb-4;
  }
}

@layer utilities {
  /* Background utilities */
  .bg-background { background-color: hsl(var(--background)); }
  .bg-background-alt { background-color: hsl(var(--background-alt)); }
  .bg-background-subtle { background-color: hsl(var(--background-subtle)); }
  .bg-foreground { background-color: hsl(var(--foreground)); }
  .bg-muted { background-color: hsl(var(--muted)); }
  .bg-accent { background-color: hsl(var(--accent)); }
  .bg-primary { background-color: hsl(var(--primary)); }
  .bg-secondary { background-color: hsl(var(--secondary)); }
  .bg-card { background-color: hsl(var(--card)); }
  .bg-popover { background-color: hsl(var(--popover)); }
  .bg-destructive { background-color: hsl(var(--destructive)); }

  /* Text utilities */
  .text-foreground { color: hsl(var(--foreground)); }
  .text-background { color: hsl(var(--background)); }
  .text-muted-foreground { color: hsl(var(--muted-foreground)); }
  .text-accent-foreground { color: hsl(var(--accent-foreground)); }
  .text-primary { color: hsl(var(--primary)); }
  .text-primary-foreground { color: hsl(var(--primary-foreground)); }
  .text-secondary-foreground { color: hsl(var(--secondary-foreground)); }
  .text-card-foreground { color: hsl(var(--card-foreground)); }
  .text-popover-foreground { color: hsl(var(--popover-foreground)); }
  .text-destructive { color: hsl(var(--destructive)); }
  .text-destructive-foreground { color: hsl(var(--destructive-foreground)); }
  .text-content-primary { color: hsl(var(--content-primary)); }
  .text-content-secondary { color: hsl(var(--content-secondary)); }
  .text-content-body { color: hsl(var(--content-body)); }
  .text-content-caption { color: hsl(var(--content-caption)); }

  /* Border utilities */
  .border-border { border-color: hsl(var(--border)); }
  .border-input { border-color: hsl(var(--input)); }
  .border-ring { border-color: hsl(var(--ring)); }
  .border-primary { border-color: hsl(var(--primary)); }
  .border-secondary { border-color: hsl(var(--secondary)); }
  .border-muted { border-color: hsl(var(--muted)); }
  .border-accent { border-color: hsl(var(--accent)); }
  .border-destructive { border-color: hsl(var(--destructive)); }

  /* Ring utilities */
  .ring-ring { --tw-ring-color: hsl(var(--ring)); }
  .ring-offset-background { --tw-ring-offset-color: hsl(var(--background)); }
}

/* Legacy Card Styles for Compatibility */
.card {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* Animation Classes */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.6s ease-out;
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}
