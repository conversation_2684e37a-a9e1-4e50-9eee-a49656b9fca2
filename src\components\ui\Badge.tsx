import React from 'react';

export interface BadgeProps extends React.HTMLAttributes<HTMLSpanElement> {
  color?: string;
}

const Badge: React.FC<BadgeProps> = ({ color = 'bg-blue-100 text-blue-800', className = '', ...props }) => {
  return (
    <span
      className={`inline-block px-2 py-0.5 rounded text-xs font-semibold ${color} ${className}`.trim()}
      {...props}
    />
  );
};

export default Badge;
